<?= $this->extend('templates/admin_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary-custom mb-2">
                                <i class="bi bi-calendar-check me-2"></i>Workplan Details
                            </h2>
                            <p class="text-muted mb-0">View detailed information about this workplan</p>
                        </div>
                        <div>
                            <a href="<?= base_url('admin/workplans') ?>" class="btn btn-secondary me-2">
                                <i class="bi bi-arrow-left me-2"></i>Back to List
                            </a>
                            <a href="<?= base_url('admin/workplans/' . $workplan['id'] . '/edit') ?>" class="btn btn-warning">
                                <i class="bi bi-pencil me-2"></i>Edit Workplan
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Workplan Information -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-info-circle me-2"></i>Workplan Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">

                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Status</label>
                                <div>
                                    <?php
                                    $statusClass = '';
                                    $statusIcon = '';
                                    switch ($workplan['status']) {
                                        case 'active':
                                            $statusClass = 'bg-success';
                                            $statusIcon = 'bi-check-circle';
                                            break;
                                        case 'inactive':
                                            $statusClass = 'bg-secondary';
                                            $statusIcon = 'bi-pause-circle';
                                            break;
                                        case 'completed':
                                            $statusClass = 'bg-primary';
                                            $statusIcon = 'bi-check-circle-fill';
                                            break;
                                        case 'cancelled':
                                            $statusClass = 'bg-danger';
                                            $statusIcon = 'bi-x-circle';
                                            break;
                                        default:
                                            $statusClass = 'bg-secondary';
                                            $statusIcon = 'bi-question-circle';
                                    }
                                    ?>
                                    <span class="badge <?= $statusClass ?> fs-6">
                                        <i class="<?= $statusIcon ?> me-1"></i><?= ucfirst($workplan['status']) ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="mb-3">
                                <label class="form-label text-muted">Title</label>
                                <div class="text-light fs-5">
                                    <i class="bi bi-bookmark me-2"></i><?= esc($workplan['title']) ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Supervisor</label>
                                <div class="text-light">
                                    <i class="bi bi-person-badge me-2"></i><?= esc($workplan['supervisor_name']) ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Organization</label>
                                <div class="text-light">
                                    <i class="bi bi-building me-2"></i><?= esc($workplan['org_name']) ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Start Date</label>
                                <div class="text-light">
                                    <i class="bi bi-calendar-event me-2"></i><?= date('F d, Y', strtotime($workplan['date_from'])) ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">End Date</label>
                                <div class="text-light">
                                    <i class="bi bi-calendar-event me-2"></i><?= date('F d, Y', strtotime($workplan['date_to'])) ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <?php if (!empty($workplan['remarks'])): ?>
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Remarks</label>
                                    <div class="text-light">
                                        <i class="bi bi-chat-text me-2"></i><?= nl2br(esc($workplan['remarks'])) ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($workplan['status_remarks'])): ?>
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Status Remarks</label>
                                    <div class="text-light">
                                        <i class="bi bi-chat-square-text me-2"></i><?= nl2br(esc($workplan['status_remarks'])) ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Duration Info -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-clock me-2"></i>Duration
                    </h5>
                </div>
                <div class="card-body">
                    <?php
                    $startDate = new DateTime($workplan['date_from']);
                    $endDate = new DateTime($workplan['date_to']);
                    $interval = $startDate->diff($endDate);
                    $totalDays = $interval->days;
                    
                    $today = new DateTime();
                    $daysRemaining = 0;
                    $progress = 0;
                    
                    if ($today < $startDate) {
                        $daysRemaining = $today->diff($startDate)->days;
                        $status = 'Not Started';
                        $statusClass = 'text-warning';
                    } elseif ($today > $endDate) {
                        $status = 'Completed';
                        $statusClass = 'text-success';
                        $progress = 100;
                    } else {
                        $daysElapsed = $startDate->diff($today)->days;
                        $daysRemaining = $today->diff($endDate)->days;
                        $progress = ($daysElapsed / $totalDays) * 100;
                        $status = 'In Progress';
                        $statusClass = 'text-info';
                    }
                    ?>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-1">
                            <span class="text-muted">Progress</span>
                            <span class="<?= $statusClass ?>"><?= $status ?></span>
                        </div>
                        <div class="progress" style="height: 8px;">
                            <div class="progress-bar bg-primary" style="width: <?= $progress ?>%"></div>
                        </div>
                        <small class="text-muted"><?= number_format($progress, 1) ?>% complete</small>
                    </div>
                    
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end border-secondary">
                                <h4 class="text-primary mb-0"><?= $totalDays ?></h4>
                                <small class="text-muted">Total Days</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-warning mb-0"><?= $daysRemaining ?></h4>
                            <small class="text-muted">Days <?= $today > $endDate ? 'Overdue' : 'Remaining' ?></small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Timestamps -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-clock-history me-2"></i>Timestamps
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label text-muted">Created</label>
                        <div class="text-light">
                            <i class="bi bi-calendar-plus me-2"></i><?= date('F d, Y \a\t g:i A', strtotime($workplan['created_at'])) ?>
                        </div>
                    </div>
                    
                    <?php if ($workplan['updated_at'] !== $workplan['created_at']): ?>
                        <div class="mb-3">
                            <label class="form-label text-muted">Last Updated</label>
                            <div class="text-light">
                                <i class="bi bi-calendar-check me-2"></i><?= date('F d, Y \a\t g:i A', strtotime($workplan['updated_at'])) ?>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($workplan['status_at'])): ?>
                        <div class="mb-3">
                            <label class="form-label text-muted">Status Changed</label>
                            <div class="text-light">
                                <i class="bi bi-calendar-event me-2"></i><?= date('F d, Y \a\t g:i A', strtotime($workplan['status_at'])) ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
