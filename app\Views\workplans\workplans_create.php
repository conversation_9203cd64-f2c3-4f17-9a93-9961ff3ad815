<?= $this->extend('templates/admin_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary-custom mb-2">
                                <i class="bi bi-plus-circle me-2"></i>Create New Workplan
                            </h2>
                            <p class="text-muted mb-0">Create a new workplan for your organization</p>
                        </div>
                        <div>
                            <a href="<?= base_url('admin/workplans') ?>" class="btn btn-secondary">
                                <i class="bi bi-arrow-left me-2"></i>Back to List
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Alerts -->
    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('errors')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <strong>Please fix the following errors:</strong>
            <ul class="mb-0 mt-2">
                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                    <li><?= esc($error) ?></li>
                <?php endforeach; ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Create Form -->
    <div class="row">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="card-title text-light mb-0">
                        <i class="bi bi-form me-2"></i>Workplan Information
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" action="<?= base_url('admin/workplans/create') ?>">
                        <?= csrf_field() ?>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="supervisor_id" class="form-label text-light">
                                        Supervisor <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select form-control-dark" id="supervisor_id" name="supervisor_id" required>
                                        <option value="">Select Supervisor</option>
                                        <?php foreach ($supervisors as $supervisor): ?>
                                            <option value="<?= $supervisor['id'] ?>" 
                                                    <?= old('supervisor_id') == $supervisor['id'] ? 'selected' : '' ?>>
                                                <?= esc($supervisor['name']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="form-text text-muted">
                                        Select the supervisor responsible for this workplan
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="title" class="form-label text-light">
                                        Workplan Title <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" 
                                           class="form-control form-control-dark" 
                                           id="title" 
                                           name="title" 
                                           value="<?= old('title') ?>" 
                                           placeholder="e.g., Q1 2024 Price Collection"
                                           maxlength="200"
                                           required>
                                    <div class="form-text text-muted">
                                        Enter a descriptive title for this workplan (max 200 characters)
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="date_from" class="form-label text-light">
                                        Start Date <span class="text-danger">*</span>
                                    </label>
                                    <input type="date" 
                                           class="form-control form-control-dark" 
                                           id="date_from" 
                                           name="date_from" 
                                           value="<?= old('date_from') ?>" 
                                           required>
                                    <div class="form-text text-muted">
                                        Select the start date for this workplan
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="date_to" class="form-label text-light">
                                        End Date <span class="text-danger">*</span>
                                    </label>
                                    <input type="date" 
                                           class="form-control form-control-dark" 
                                           id="date_to" 
                                           name="date_to" 
                                           value="<?= old('date_to') ?>" 
                                           required>
                                    <div class="form-text text-muted">
                                        Select the end date for this workplan
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="remarks" class="form-label text-light">
                                        Remarks
                                    </label>
                                    <textarea class="form-control form-control-dark" 
                                              id="remarks" 
                                              name="remarks" 
                                              rows="4" 
                                              placeholder="Enter any additional notes or remarks about this workplan..."><?= old('remarks') ?></textarea>
                                    <div class="form-text text-muted">
                                        Optional: Add any additional information about this workplan
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle me-2"></i>
                                    <strong>Note:</strong> The workplan status will be automatically set to "Active" upon creation.
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="<?= base_url('admin/workplans') ?>" class="btn btn-secondary">
                                        <i class="bi bi-x-circle me-2"></i>Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-check-circle me-2"></i>Create Workplan
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set minimum date to today for both date inputs
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('date_from').setAttribute('min', today);
    document.getElementById('date_to').setAttribute('min', today);
    
    // Update end date minimum when start date changes
    document.getElementById('date_from').addEventListener('change', function() {
        const startDate = this.value;
        const endDateInput = document.getElementById('date_to');
        
        if (startDate) {
            // Set minimum end date to the day after start date
            const minEndDate = new Date(startDate);
            minEndDate.setDate(minEndDate.getDate() + 1);
            endDateInput.setAttribute('min', minEndDate.toISOString().split('T')[0]);
            
            // Clear end date if it's before the new minimum
            if (endDateInput.value && endDateInput.value <= startDate) {
                endDateInput.value = '';
            }
        }
    });
    
    // Validate that end date is after start date
    document.getElementById('date_to').addEventListener('change', function() {
        const startDate = document.getElementById('date_from').value;
        const endDate = this.value;
        
        if (startDate && endDate && endDate <= startDate) {
            alert('End date must be after start date.');
            this.value = '';
        }
    });
});
</script>
<?= $this->endSection() ?>
